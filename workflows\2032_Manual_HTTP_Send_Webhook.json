{"id": "zAkPoRdcG5M5x4KT", "meta": {"instanceId": "a897062ac3223eacd9c7736276b653c446bc776a63cde2a42a2949ad984f7092"}, "name": "Perform an email search with Icypeas (single)", "tags": [], "nodes": [{"id": "7bd55522-62dd-40da-939d-e10c185dd44d", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [1220, 480], "parameters": {}, "typeVersion": 1}, {"id": "422bb377-afe7-4332-a134-15af150e8006", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [1060, 220], "parameters": {"height": 243.*************, "content": "## Perform an email search with Icypeas (single)\n\nThis workflow demonstrates how to perform an email search using Icypeas. Visit https://icypeas.com to create your account.\n\n\n"}, "typeVersion": 1}, {"id": "a95bd610-e5e3-4343-afcc-4af22dca1f8f", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [1367, 296], "parameters": {"width": 506, "height": 1042.*************, "content": "## Authenticates to your Icypeas account\n\nThis code node utilizes your API key, API secret, and User ID to establish a connection with your Icypeas account.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nOpen this node and insert your API Key, API secret, and User ID within the quotation marks. You can locate these credentials on your Icypeas profile at https://app.icypeas.com/bo/profile. Here is the extract of what you have to change :\n\nconst API_KEY = \"**PUT_API_KEY_HERE**\";\nconst API_SECRET = \"**PUT_API_SECRET_HERE**\";\nconst USER_ID = \"**PUT_USER_ID_HERE**\";\n\nDo not change any other line of the code.\n\nIf you are a self-hosted user, follow these steps to activate the crypto module :\n\n1.Access your n8n instance:\nLog in to your n8n instance using your web browser by navigating to the URL of your instance, for example: http://your-n8n-instance.com.\n\n2.Go to Settings:\nIn the top-right corner, click on your username, then select \"Settings.\"\n\n3.Select General Settings:\nIn the left menu, click on \"General.\"\n\n4.Enable the Crypto module:\nScroll down to the \"Additional Node Packages\" section. You will see an option called \"crypto\" with a checkbox next to it. Check this box to enable the Crypto module.\n\n5.Save the changes:\nAt the bottom of the page, click \"Save\" to apply the changes.\n\nOnce you've followed these steps, the Crypto module should be activated for your self-hosted n8n instance. Make sure to save your changes and optionally restart your n8n instance for the changes to take effect.\n\n\n\n\n\n\n\n\n\n\n\n"}, "typeVersion": 1}, {"id": "f0951515-48cf-4c1b-82fd-960959a51bb7", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1873, 300], "parameters": {"width": 492, "height": 961.************, "content": "## Performs an email search on your Icypeas account\n\n\nThis node executes an HTTP request (POST) to search for an email address using Icypeas.\n\n\n\n\n\n\n\n\n\n\n\n\n\n### You need to create credentials in the HTTP Request node :\n\n➔ In the Credential for <PERSON><PERSON>, click on - Create new Credential -.\n➔ In the Name section, write “Authorization”\n➔ In the Value section, select expression (located just above the field on the right when you hover on top of it) and write {{ $json.api.key + ':' + $json.api.signature }} .\n➔ Then click on “Save” to save the changes.\n\n### To search for the email address :\n\n➔ go to the Body Parameters section,\n➔ create a new parameter,\n➔ enter \"lastname\" in the Name field.\n➔ put the lastname of the person whose email you want.\n\n➔ go to the Body Parameters section,\n➔ create a new parameter,\n➔ enter \"firstname\" in the Name field.\n➔ put the firstname of the person whose email you want.\n\n➔ go to the Body Parameters section,\n➔ create a new parameter,\n➔ enter \"domainOrCompany\" in the Name field.\n➔ put the domain/company name of the person whose email you want.\n\n\n\nYou will find the result here : https://app.icypeas.com/bo/singlesearch?task=email-search\n"}, "typeVersion": 1}, {"id": "6d12e09f-143a-46f1-9790-512d4f10f51f", "name": "Authenticates to your Icypeas account", "type": "n8n-nodes-base.code", "position": [1560, 480], "parameters": {"jsCode": "const BASE_URL = \"https://app.icypeas.com\";\nconst PATH = \"/api/domain-search\";\nconst METHOD = \"POST\";\n\n// Change here\nconst API_KEY = \"PUT_API_KEY_HERE\";\nconst API_SECRET = \"PUT_API_SECRET_HERE\";\nconst USER_ID = \"PUT_USER_ID_HERE\";\n////////////////\n\nconst genSignature = (\n    path,\n    method,\n    secret,\n    timestamp = new Date().toISOString()\n) => {\n    const Crypto = require('crypto');\n    const payload = `${method}${path}${timestamp}`.toLowerCase();\n    const sign = Crypto.createHmac(\"sha1\", secret).update(payload).digest(\"hex\");\n\n    return sign;\n};\n\nconst fullPath = `${BASE_URL}${PATH}`;\n$input.first().json.api = {\n  timestamp: new Date().toISOString(),\n  secret: API_SECRET,\n  key: API_KEY,\n  userId: USER_ID,\n  url: fullPath,\n};\n$input.first().json.api.signature = genSignature(PATH, METHOD, API_SECRET, $input.first().json.api.timestamp);\nreturn $input.first();"}, "typeVersion": 1}, {"id": "5f62f87f-7a25-4030-bcd4-d87b24269504", "name": "Run email search (single)", "type": "n8n-nodes-base.httpRequest", "position": [1940, 480], "parameters": {"url": "={{ $json.api.url }}", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "lastname", "value": "=Landoin"}, {"name": "firstname", "value": "<PERSON>"}, {"name": "domainOrCompany", "value": "icypeas"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{"name": "X-ROCK-TIMESTAMP", "value": "={{ $json.api.timestamp }}"}]}}, "credentials": {"httpHeaderAuth": {"id": "KGXtUrqC6lNLwW2w", "name": "Header Auth account"}}, "typeVersion": 4.1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "34ee6b2d-673e-4d5d-a0b2-7c7a4af14d3c", "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Authenticates to your Icypeas account", "type": "main", "index": 0}]]}, "Authenticates to your Icypeas account": {"main": [[{"node": "Run email search (single)", "type": "main", "index": 0}]]}}}