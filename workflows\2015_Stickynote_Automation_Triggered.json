{"id": "xRclXA5QzrT3c6U8", "meta": {"instanceId": "8931e7db592c2960ce253801ea290c1dc66e447734ce3d968310365665cefc80"}, "name": "Discord MCP Chat Agent", "tags": [], "nodes": [{"id": "3c008773-802c-461c-9350-f42dc5f3969c", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [100, -440], "parameters": {"options": {}}, "typeVersion": 1.9}, {"id": "9b5bd212-19bc-4303-a934-b783f7cb5ea7", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "position": [-160, -440], "webhookId": "79281a20-6afe-4188-ae87-cc80be737ad7", "parameters": {"options": {}}, "typeVersion": 1.1}, {"id": "32a7152e-47ea-4859-aa35-f220a69ddb0d", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [20, -240], "parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o", "cachedResultName": "gpt-4o"}, "options": {}}, "credentials": {"openAiApi": {"id": "AWozvbIHWTdrKYZt", "name": "OpenAi account"}}, "typeVersion": 1.2}, {"id": "bc9204f7-0116-43cc-947d-8d2b883fc2c3", "name": "Discord MCP Client", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [340, -240], "parameters": {"sseEndpoint": "http://localhost:5678/mcp/404f083e-f3f4-4358-83ef-9804099ee253/sse"}, "typeVersion": 1}, {"id": "e42dc3a5-5463-4198-b691-ff8e9d6fc892", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [-340, -700], "parameters": {"width": 280, "height": 360, "content": "## Natural Language Input\nYou can call from another workflow, hit the chat endpoint, or even hit from another Discord bot if you wanted to! Any natural language command should work fine - let me know if you manage to break something and I will look at updating the template!"}, "typeVersion": 1}, {"id": "c44b730e-fe1b-4290-a26e-aed04852ccdc", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [20, -700], "parameters": {"width": 220, "height": 540, "content": "## Tool enabled agent\nIf you are going to swap the model out, just make sure that it's one that can handle tools. No special system prompt should be needed for the large cloud models, if you go with a quantized model via Ollama then you might need to coax it a bit."}, "typeVersion": 1}, {"id": "8761f368-e20a-48ab-bfff-1d4e6401d269", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [340, -700], "parameters": {"height": 540, "content": "## Discord MCP Client/Server\nThis is totally customizable (you can connect it to any MCP server by changing the URL), but if you need a starting point, you can check out my \"Manage your discord server with natural language from anywhere\" template as a starting point."}, "typeVersion": 1}], "active": true, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "cdc83b62-051a-4a98-8d25-3637b3da0523", "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Discord MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}}