{"name": "Deep Research Report Generation Using Open Router, Google Search, Webhook/Telegram and Notion", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [-860, 180], "id": "db0c8ef2-4859-4df9-a29b-4066998e7926", "name": "<PERSON>eg<PERSON>", "webhookId": "13911073-fffc-490c-b05b-3628d7a6faa5"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json?.message?.chat?.id ||  $json?.body?.session_id }}", "contextWindowLength": 10}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-480, 480], "id": "49a10f75-eaa2-4466-a568-edfe9084fe30", "name": "Simple Memory"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n   \"is_pass_next\" : \"boolean\",\n   \"message\" : \"string\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-320, 480], "id": "3c820502-6a10-4ad7-b460-42ea297b0a18", "name": "Structured Output Parser"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.output.is_pass_next }}", "rightValue": "", "operator": {"type": "boolean", "operation": "false", "singleValue": true}, "id": "9e5f68a3-6af4-48ce-9bf6-6c6e06236301"}], "combinator": "and"}, "renameOutput": true, "outputKey": "<PERSON><PERSON><PERSON>"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "ac64b26c-d9e6-48a1-9fff-8b85156725b2", "leftValue": "={{ $json.output.is_pass_next }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Pass"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-220, 300], "id": "cb67de10-2789-4c01-afcb-7716005419e8", "name": "Switch"}, {"parameters": {"promptType": "define", "text": "=You are the research and planning agent. Your role is to help users plan high-quality research content — quickly, clearly, and efficiently.\n\nUser input: {{ $json?.message?.text ||  $json?.body?.message}}\n\n🌟 Your Mission:\nAfter greeting message ask what user want to research about. Just ask What would you like to research?\n\nGiven the following research topic from the user, ask some follow up questions to clarify the research direction. Return a maximum of 3 questions, but feel free to return less if the original query is clear. Ask all questions one by one.\n\nAfter clarity questions send draft for user to confirm. \n\n🧠 OUTPUT FORMAT (Always use this JSON output structure):\n\nIf needs feedback or clarity from user: \n\n{\n  \"is_pass_next\": false,\n  \"message\": \"message\"\n}\n\nIf strategy is ready for confirmation:\n{\n  \"is_pass_next\": false,\n  \"message\": \"Here’s your research plan draft:\"\n}\n\n🚀 If user confirms:\n{\n  \"is_pass_next\": true,\n  \"message\": \"The research plan is as follow:\",\n}\n\nToday's date : {{ $now }}", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [-560, 300], "id": "c3acda96-3dfe-4562-85dc-508abb2b4e6a", "name": "Strategy Agent"}, {"parameters": {"promptType": "define", "text": "=Given the following prompt from the user, generate a list of SERP queries to research the topic.\nReduce the number of words in each query to its keywords only.\nReturn a maximum of 3 queries, but feel free to return less if the original prompt is clear. Make sure each query is unique and not similar to each other: <prompt>{{ $('Switch').item.json.output.message }}</prompt>", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [880, 400], "id": "cc6ede05-c8e2-4d95-8f14-a2bd458d156c", "name": "Search Query Agent"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"queries\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"query\": {\n            \"type\": \"string\",\n            \"description\": \"The SERP query\"\n          },\n          \"researchGoal\": {\n            \"type\": \"string\",\n            \"description\": \"First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions.\"\n          }\n        }\n      }\n    }\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1060, 580], "id": "e377ea10-0533-4eb9-924c-a812ef08cf11", "name": "Structured Output Parser1"}, {"parameters": {"fieldToSplitOut": "output.queries", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1240, 500], "id": "430256b3-1d07-4c83-90c9-3d836833332d", "name": "Split Out"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1420, 500], "id": "07630239-b8f2-4a00-904d-b2fb59c04545", "name": "Loop Over Queries"}, {"parameters": {"method": "POST", "url": "https://api.tavily.com/search", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $json.query }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1640, 600], "id": "64d8824c-2914-4f46-b0ca-9dff584970d7", "name": "HTTP Request"}, {"parameters": {"assignments": {"assignments": [{"id": "00d1543a-0036-43a3-8034-14bc29317218", "name": "tavily_results", "value": "={{ $json.results }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1860, 600], "id": "d0c8337a-c631-4582-a9bd-f7166772787f", "name": "<PERSON>"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4.1-mini", "mode": "list", "cachedResultName": "GPT-4.1-MINI"}, "messages": {"values": [{"content": "=You are an intelligent assistant. A user has asked the following query:\n\n[Search Query]: {{ $('HTTP Request').item.json.query }}\n\nBelow is the draft for research that user has passed: {{ $('Switch').item.json.output.message }}\n\nBelow are the search results retrieved from the internet (from Tavily):\n\n{{ $json.tavily_results }}\n\nEach result includes a title, URL, and content. From these, choose the **single most relevant URL** that best matches the user's query. Focus on accuracy, relevance, and depth of the content. Only return the URL — do not include any explanation or extra text.\n\nreturn it like below JSON format: \n{\n final_url: \"url\"\n}\n"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2060, 600], "id": "fa94faaa-c5a1-4752-a9e6-69c670217c65", "name": "OpenAI"}, {"parameters": {"method": "POST", "url": "https://api.tavily.com/extract", "authentication": "genericCredentialType", "genericAuthType": "httpCustomAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "urls", "value": "={{ $json.message.content.final_url }}"}, {"name": "extract_depth", "value": "advanced"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2420, 600], "id": "e3801e7f-60f5-4e9b-8ff5-717316c836c0", "name": "HTTP Request1"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1620, 240], "id": "86646bdb-78ba-440f-816e-6d8400c1dab7", "name": "Aggregate"}, {"parameters": {"promptType": "define", "text": "=You are a research and writing assistant.\n\nYour task is to generate a comprehensive and well-structured blog-style report based on the following research topic and raw extracted content. Use professional, clear language suitable for a wide audience. Organize the report using headings and subheadings. Avoid repetition. At the end of the report, include a \"Sources\" section with a list of the URLs used. \n\nThis is the final draft on which you need to create report from given topic and draft: \n{{ $('Switch').item.json.output.message }}. Try to create final report from this outline and draft.\n\n---\n**Extracted Content**:\n\n1. Source: {{ $json.data[0].results[0].url }}\nContent:{{ $json.data[0].results[0].raw_content }}\n\n\n2. Source: {{ $json.data[1].results[0].url }}\nContent: {{ $json.data[1].results[0].raw_content }}\n\n3. Source: {{ $json.data[2].results[0].url }}\nContent:{{ $json.data[2].results[0].raw_content }}\n\n---\n\n**Instructions**:\n- Make as detailed report as possible. Include all the useful information.\n- Analyze and synthesize the information from all sources.\n- Structure the report into meaningful sections with headings and subheadings (e.g., Introduction, Key Insights, Challenges, Opportunities, Conclusion, etc.).\n- Do not copy the content verbatim — rewrite and consolidate it into an original, cohesive narrative.\n- Maintain factual accuracy.\n- Make it as as detailed as possible, aim for 3 or more pages, include ALL the learnings from research.\n- Format the report in markdown. Use headings, lists and tables only and where appropriate.\n- At the end of **each paragraph**, insert a superscript source reference in markdown format like this: `[1]`, `[2]`, `[3]`, based on which source(s) the paragraph is derived from.\n- Do not mention the source URL in the paragraph body.\n- Do not include content that cannot be mapped to one of the sources.\n- At the end include sources link with correct url.\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1820, 240], "id": "59c8f32a-7208-4a2d-af97-50fbe2f23e93", "name": "AI Agent"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=You will be given research draft that user asked to you need to create title and description using this draft. \n\ndraft:  {{ $('Switch').item.json.output.message }}\n\noutput using below json format: \n{\n \"title\": string,\n \"description\": string\n}"}]}, "jsonOutput": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [300, 420], "id": "7755fe19-6591-45a6-b763-31f239f41dd8", "name": "OpenAI1"}, {"parameters": {"resource": "databasePage", "databaseId": {"__rl": true, "value": "1f536e90-e9d0-805c-a1c1-f2fab42a8a7b", "mode": "list", "cachedResultName": "n8n DeepResearch", "cachedResultUrl": "https://www.notion.so/1f536e90e9d0805ca1c1f2fab42a8a7b"}, "title": "={{ $json.message.content.title }}", "propertiesUi": {"propertyValues": [{"key": "Request ID|rich_text", "textContent": "={{ $('Code').item.json.randomId.toString() }}"}, {"key": "Name|title", "title": "={{ $json.message.content.title }}"}, {"key": "Description|rich_text", "textContent": "={{ $json.message.content.description }}"}, {"key": "Created time|date", "date": "={{ $now.toISO() }}"}, {"key": "Status|status", "statusValue": "In progress"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [660, 440], "id": "09b6efbc-7326-4b38-b9e9-49ff87b32101", "name": "Notion"}, {"parameters": {"resource": "databasePage", "operation": "update", "pageId": {"__rl": true, "value": "={{ $('Convert to HTML').item.json.id }}", "mode": "id"}, "propertiesUi": {"propertyValues": [{"key": "Status|status", "statusValue": "Done"}, {"key": "Last Updated|date", "date": "={{ $now.toISO() }}"}]}, "options": {}}, "type": "n8n-nodes-base.notion", "typeVersion": 2.2, "position": [4400, 320], "id": "cb35b466-f368-4b57-90c1-fe45e8566516", "name": "Notion1"}, {"parameters": {"method": "PATCH", "url": "=https://api.notion.com/v1/blocks/{{ $('Convert to HTML').item.json.id }}/children", "authentication": "predefinedCredentialType", "nodeCredentialType": "notionApi", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Notion-Version", "value": "2022-06-28"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{\n{\n  \"children\": $json.block\n}\n}}", "options": {"timeout": "={{ 1000 * 60 }}"}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [4120, 520], "id": "1e8271df-33ea-4ce6-b49a-4f9a7d809f16", "name": "HTTP Request2"}, {"parameters": {"mode": "markdownToHtml", "markdown": "={{ $('AI Agent').item.json.output }}", "options": {"tables": true}}, "id": "76b8f99e-330a-4daf-a524-677e21a5f1bc", "name": "Convert to HTML", "type": "n8n-nodes-base.markdown", "position": [2440, 340], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "851b8a3f-c2d3-41ad-bf60-4e0e667f6c58", "name": "tag", "type": "array", "value": "={{ $json.data.match(/<table[\\s\\S]*?<\\/table>|<ul[\\s\\S]*?<\\/ul>|<[^>]+>[^<]*<\\/[^>]+>/g) }}"}]}, "options": {}}, "id": "dc13b8f8-b1af-49b9-bac8-9237eda3d4c5", "name": "HTML to Array", "type": "n8n-nodes-base.set", "position": [2660, 340], "typeVersion": 3.4}, {"parameters": {"fieldToSplitOut": "tag", "options": {}}, "id": "a3434303-05dc-4d8c-982e-c11373976a1e", "name": "Tags to Items", "type": "n8n-nodes-base.splitOut", "position": [2860, 380], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "={{ $json.tag.trim() }}", "messages": {"messageValues": [{"message": "=Convert the following html into its equivalent Notion Block as per Notion's API schema.\n* Ensure the content is always included and remains the same.\n* Return only a json response.\n* Generate child-level blocks. Should not define \"parent\" or \"children\" property.\n* Strongly prefer headings, paragraphs, tables and lists type blocks.\n* available headings are heading_1, heading_2 and heading_3 - h4,h5,h6 should use heading_3 type instead. ensure headings use the rich text definition.\n* ensure lists blocks include all list items.\n\n## Examples\n\n1. headings\n```\n<h3 id=\"references\">References</h3>\n```\nwould convert to \n```\n{\"object\":  \"block\", \"type\": \"heading_3\", \"heading_3\": { \"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"References\"}}]}}\n```\n\n2. lists\n```\n<ul><li>hello</li><li>world</li></ul>\n```\nwould convert to\n```\n[\n{\n  \"object\": \"block\",\n  \"type\": \"bulleted_list_item\",\n  \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"hello\"}}]}\n},\n{\n  \"object\": \"block\",\n  \"type\": \"bulleted_list_item\",\n  \"bulleted_list_item\": {\"rich_text\": [{\"type\": \"text\",\"text\": {\"content\": \"world\"}}]}\n}\n]\n```\n\n3. tables\n```\n<table>\n  <thead>\n    <tr><th>Technology</th><th>Potential Impact</th></tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>5G Connectivity</td><td>Enables faster data speeds and advanced apps</td>\n    </tr>\n  </tbody>\n</table>\n```\nwould convert to\n```\n{\n  \"object\": \"block\",\n  \"type\": \"table\",\n  \"table\": {\n    \"table_width\": 2,\n    \"has_column_header\": true,\n    \"has_row_header\": false,\n    \"children\": [\n      {\n        \"object\": \"block\",\n        \"type\": \"table_row\",\n        \"table_row\": {\n          \"cells\": [\n            [\n              {\n                \"type\": \"text\",\n                \"text\": {\n                  \"content\": \"Technology\",\n                  \"link\": null\n                }\n              },\n              {\n                \"type\": \"text\",\n                \"text\": {\n                  \"content\": \"Potential Impact\",\n                  \"link\": null\n                }\n              }\n            ],\n            [\n              {\n                \"type\": \"text\",\n                \"text\": {\n                  \"content\": \"5G Connectivity\",\n                  \"link\": null\n                }\n              },\n              {\n                \"type\": \"text\",\n                \"text\": {\n                  \"content\": \"Enables faster data speeds and advanced apps\",\n                  \"link\": null\n                }\n              }\n            ]\n          ]\n        }\n      }\n    ]\n  }\n}\n```\n4. anchor links\nSince Notion doesn't support anchor links, just convert them to rich text blocks instead.\n```\n<a href=\"#module-0-pre-course-setup-and-learning-principles\">Module 0: Pre-Course Setup and Learning Principles</a>\n```\nconverts to\n```\n{\n  \"object\": \"block\",\n  \"type\": \"paragraph\",\n  \"paragraph\": {\n    \"rich_text\": [\n      {\n        \"type\": \"text\",\n        \"text\": {\n          \"content\": \"Module 0: Pre-Course Setup and Learning Principles\"\n        }\n      }\n    ]\n  }\n}\n```\n5. Invalid html parts\nWhen the html is not syntax valid eg. orphaned closing tags, then just skip the conversion and use an empty rich text block.\n```\n</li>\\n</ol>\n```\ncan be substituted with\n```\n{\n  \"object\": \"block\",\n  \"type\": \"paragraph\",\n  \"paragraph\": {\n    \"rich_text\": [\n      {\n        \"type\": \"text\",\n        \"text\": {\n          \"content\": \" \"\n        }\n      }\n    ]\n  }\n}\n```"}]}}, "id": "0c472a0a-4c4a-4421-b98c-e1fe84a5da4d", "name": "Notion Block Generator", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [3040, 360], "typeVersion": 1.5}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3040, 540], "id": "bcefff85-b26a-4bc5-a131-f2582da4c99c", "name": "Google Gemini Chat Model"}, {"parameters": {"assignments": {"assignments": [{"id": "73fcb8a0-2672-4bd5-86de-8075e1e02baf", "name": "=block", "type": "array", "value": "={{\n(function(){\n  const block = $json.response.text\n    .replace('```json', '')\n    .replace('```', '')\n    .trim()\n    .parseJson();\n  if (Array.isArray(block)) return block;\n  if (block.type.startsWith('heading_')) {\n    const prev = Number(block.type.split('_')[1]);\n    const next = Math.max(1, prev - 1);\n    if (next !== prev) {\n      block.type = `heading_${next}`;\n      block[`heading_${next}`] = Object.assign({}, block[`heading_${prev}`]);\n      block[`heading_${prev}`] = undefined;\n    }\n  }\n  return [block];\n})()\n}}"}]}, "options": {}}, "id": "991e572e-5949-4f6b-89e4-8f99db69955d", "name": "Parse JSON blocks", "type": "n8n-nodes-base.set", "position": [3380, 360], "executeOnce": false, "typeVersion": 3.4, "onError": "continueRegularOutput"}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "f68cefe0-e109-4d41-9aa3-043f3bc6c449", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.error }}", "rightValue": ""}]}, "options": {}}, "id": "bbb56ba0-6f86-45ba-8d26-78be926bbd98", "name": "Valid Blocks", "type": "n8n-nodes-base.filter", "position": [3580, 360], "typeVersion": 2.2}, {"parameters": {"options": {}}, "id": "7ff6ecda-0d72-49c8-b0af-65756a04c76a", "name": "For Each Block...", "type": "n8n-nodes-base.splitInBatches", "position": [3900, 400], "typeVersion": 3}, {"parameters": {"resource": "databasePage", "operation": "getAll", "databaseId": {"__rl": true, "value": "1f536e90-e9d0-805c-a1c1-f2fab42a8a7b", "mode": "list", "cachedResultName": "n8n DeepResearch", "cachedResultUrl": "https://www.notion.so/1f536e90e9d0805ca1c1f2fab42a8a7b"}, "limit": 1, "filterType": "manual", "matchType": "allFilters", "filters": {"conditions": [{"key": "Request ID|rich_text", "condition": "equals", "richTextValue": "={{ $('Code').item.json.randomId.toString() }}"}]}, "options": {}}, "id": "82c33ad1-1e8b-4bdb-adfe-b25c16fe782b", "name": "Get Existing Row", "type": "n8n-nodes-base.notion", "position": [2220, 260], "typeVersion": 2.2}, {"parameters": {"jsCode": "const randomId = Math.floor(100000 + Math.random() * 900000);\nreturn { randomId };\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [140, 420], "id": "fed5e77e-22a0-44b6-9c61-e2faa2bba26c", "name": "Code"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [4120, 300], "id": "014c40d1-7c82-4b04-a907-9cab832df4c0", "name": "Aggregate1"}, {"parameters": {"model": "anthropic/claude-3.5-sonnet", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-640, 480], "id": "a8aa4844-e19e-4f11-bcc4-4b7ac7d11412", "name": "OpenRouter <PERSON>"}, {"parameters": {"model": "anthropic/claude-3.5-sonnet", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [860, 580], "id": "68c4f8d0-5d3f-48ee-9c84-40664992a4e5", "name": "OpenRouter Chat Model1"}, {"parameters": {"model": "anthropic/claude-3.5-sonnet", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [1800, 400], "id": "a24cd1e0-eda2-4dcb-a527-aec509cf8381", "name": "OpenRouter Chat Model2"}, {"parameters": {"httpMethod": "POST", "path": "1c86c408-aeed-40c5-b4ba-aad5f4cdf0ad", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-860, 360], "id": "167770e1-31e6-4830-a1b8-b3bf65db779c", "name": "Webhook", "webhookId": "1c86c408-aeed-40c5-b4ba-aad5f4cdf0ad"}, {"parameters": {"respondWith": "text", "responseBody": "={{ $json.output.message }}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [20, 160], "id": "09b175ea-164b-4e22-bd43-7bf94ee2788c", "name": "Respond to Webhook"}, {"parameters": {"respondWith": "text", "responseBody": "Thank you for your response. We are preparing your report. Once it is finished we will send report link to you.", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-60, 420], "id": "3486459a-4816-4cd0-a635-eba98e91ae6c", "name": "Respond to Webhook1"}, {"parameters": {"method": "POST", "url": "https://deep-research-hub-saptarshi3.replit.app/api/webhook/report-ready", "sendBody": true, "bodyParameters": {"parameters": [{"name": "report_title", "value": "={{ $json.name || '' }}"}, {"name": "report_url", "value": "={{ $json.url || \"\" }}"}, {"name": "status", "value": "={{ $json.property_status || \"\" }}"}, {"name": "session_id", "value": "={{ $('Webhook').item.json.body.session_id || \"\" }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [4640, 500], "id": "ab8b66ad-ed6b-492d-94f9-337173f6cfed", "name": "HTTP Request3"}, {"parameters": {"content": "# Workflow Overview\n\nThis workflow automates the process of research planning, query generation, web search, content extraction, and report creation. It integrates with Webhook, Notion, OpenAI, and external APIs.\n\n## Main Steps\n\n1. **Trigger**: Starts from a Telegram message or Webhook.\n2. **Strategy Agent**: Asks the user for a research topic and clarifies requirements.\n3. **Query Generation**: Generates SERP queries for the research topic.\n4. **Web Search**: Uses Tavily API to fetch search results for each query.\n5. **Content Extraction**: Extracts content from the most relevant URLs.\n6. **Report Generation**: Uses AI to synthesize a detailed report in markdown.\n7. **Notion Integration**: Saves the report and metadata to a Notion database.\n8. **Notification**: Notifies the user when the report is ready.\n\n## Key Nodes\n\n- **Trigger**: Listens for new messages.\n- **Strategy Agent**: Handles user interaction and planning.\n- **Search Query Agent**: Generates search queries.\n- **HTTP Request**: Fetches search results and extracts content.\n- **OpenAI/AI Agent**: Processes and synthesizes information.\n- **Notion**: Stores the final report.\n- **Sticky Note**: Use this to add or update instructions.\n\n## Tips\n\n- Update credentials for all API nodes before running.\n- You can edit or move Sticky Notes for better documentation.\n- Use the Switch node to handle user confirmations and feedback.\n- Optionally use Telegram or any other third party integration to trigger the workflow.", "height": 940, "width": 620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, -920], "id": "9b00f677-b15d-4c00-aa06-df10decbd38d", "name": "<PERSON><PERSON>"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[]]}, "Simple Memory": {"ai_memory": [[{"node": "Strategy Agent", "type": "ai_memory", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Strategy Agent", "type": "ai_outputParser", "index": 0}]]}, "Switch": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}], [{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "Strategy Agent": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Search Query Agent", "type": "ai_outputParser", "index": 0}]]}, "Search Query Agent": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Loop Over Queries", "type": "main", "index": 0}]]}, "Loop Over Queries": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Loop Over Queries", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Get Existing Row", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "Notion", "type": "main", "index": 0}]]}, "Notion1": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}]]}, "Convert to HTML": {"main": [[{"node": "HTML to Array", "type": "main", "index": 0}]]}, "HTML to Array": {"main": [[{"node": "Tags to Items", "type": "main", "index": 0}]]}, "Tags to Items": {"main": [[{"node": "Notion Block Generator", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Notion Block Generator", "type": "ai_languageModel", "index": 0}]]}, "Notion Block Generator": {"main": [[{"node": "Parse JSON blocks", "type": "main", "index": 0}]]}, "Parse JSON blocks": {"main": [[{"node": "Valid Blocks", "type": "main", "index": 0}]]}, "Valid Blocks": {"main": [[{"node": "For Each Block...", "type": "main", "index": 0}]]}, "For Each Block...": {"main": [[{"node": "Aggregate1", "type": "main", "index": 0}], [{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "For Each Block...", "type": "main", "index": 0}]]}, "Get Existing Row": {"main": [[{"node": "Convert to HTML", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "OpenAI1", "type": "main", "index": 0}]]}, "Aggregate1": {"main": [[{"node": "Notion1", "type": "main", "index": 0}]]}, "Notion": {"main": [[{"node": "Search Query Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "Strategy Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenRouter Chat Model1": {"ai_languageModel": [[{"node": "Search Query Agent", "type": "ai_languageModel", "index": 0}]]}, "OpenRouter Chat Model2": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "Strategy Agent", "type": "main", "index": 0}]]}, "Respond to Webhook1": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "b12bd622-3a4c-4197-ae0e-8853d87fb2d3", "meta": {"instanceId": "88bba54a6622a369cbc7b9f664b6b7498793423eedd66abbbb5559f0f452a57e"}, "id": "3ycewf83b8KVQi8N", "tags": []}