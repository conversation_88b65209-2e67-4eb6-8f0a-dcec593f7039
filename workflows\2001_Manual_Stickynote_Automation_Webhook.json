{"id": "wTI77cpLkbxsRQat", "meta": {"instanceId": "885b4fb4a6a9c2cb5621429a7b972df0d05bb724c20ac7dac7171b62f1c7ef40", "templateCredsSetupCompleted": true}, "name": "Brand Content Extract, Summarize & Sentiment Analysis with Bright Data", "tags": [{"id": "Kujft2FOjmOVQAmJ", "name": "Engineering", "createdAt": "2025-04-09T01:31:00.558Z", "updatedAt": "2025-04-09T01:31:00.558Z"}, {"id": "ddPkw7Hg5dZhQu2w", "name": "AI", "createdAt": "2025-04-13T05:38:08.053Z", "updatedAt": "2025-04-13T05:38:08.053Z"}], "nodes": [{"id": "646ef542-c601-4103-87e6-6fa9616d8c52", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [120, -560], "parameters": {}, "typeVersion": 1}, {"id": "00b4ce90-c4f2-41c4-8943-7db3d0c3f81a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [100, -320], "parameters": {"width": 400, "height": 300, "content": "## Note\n\nThis workflow deals with the brand content extraction by utilizing the Bright Data Web Unlocker Product.\n\nThe Basic LLM Chain, Information Extraction, Summarization Chain are being used to demonstrate the usage of the N8N AI capabilities.\n\n**Please make sure to set the web URL of your interest within the \"Set URL and Bright Data Zone\" node and update the Webhook Notification URL**"}, "typeVersion": 1}, {"id": "5cc35b9b-7483-404e-96a3-1688f7b9078b", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [540, -320], "parameters": {"width": 480, "height": 300, "content": "## LLM Usages\n\nGoogle Gemini Flash Exp model is being used.\n\nBasic LLM Chain Data Extractor.\n\nInformation Extraction is being used for the handling the custom sentiment analysis with the structured response.\n\nSummarization Chain is being used for the creation of a concise summary of the extracted brand content."}, "typeVersion": 1}, {"id": "e15f32de-58d9-4ea6-9d5c-f63975d1090d", "name": "Markdown to Textual Data Extractor", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [1240, -440], "parameters": {"text": "=You need to analyze the below markdown and convert to textual data. Please do not output with your own thoughts. Make sure to output with textual data only with no links, scripts, css etc.\n\n{{ $json.data }}", "messages": {"messageValues": [{"message": "You are a markdown expert"}]}, "promptType": "define"}, "typeVersion": 1.6}, {"id": "1462cd3b-b1d5-4ddf-9f1e-2b8f20faa19c", "name": "Set URL and Bright Data Zone", "type": "n8n-nodes-base.set", "position": [340, -560], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "3aedba66-f447-4d7a-93c0-8158c5e795f9", "name": "url", "type": "string", "value": "https://www.amazon.com/TP-Link-Dual-Band-Archer-BE230-HomeShield/dp/B0DC99N2T8"}, {"id": "4e7ee31d-da89-422f-8079-2ff2d357a0ba", "name": "zone", "type": "string", "value": "web_unlocker1"}]}}, "typeVersion": 3.4}, {"id": "9783e878-e864-4632-9b89-d78567204053", "name": "AI Sentiment Analyzer with the structured response", "type": "@n8n/n8n-nodes-langchain.informationExtractor", "position": [1740, 100], "parameters": {"text": "=Perform the sentiment analysis on the below content and output with the structured information.\n\nHere's the content:\n\n{{ $('Perform Bright Data Web Request').item.json.data }}", "options": {"systemPromptTemplate": "You are an expert sentiment analyzer."}, "schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"http://json-schema.org/schema#\",\n  \"title\": \"SentimentAnalysisResponseArray\",\n  \"type\": \"array\",\n  \"items\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"sentiment\": {\n        \"type\": \"string\",\n        \"enum\": [\"Positive\", \"Neutral\", \"Negative\"],\n        \"description\": \"The overall sentiment of the content.\"\n      },\n      \"confidence_score\": {\n        \"type\": \"number\",\n        \"minimum\": 0,\n        \"maximum\": 1,\n        \"description\": \"Confidence score of the sentiment classification.\"\n      },\n      \"sentence\": {\n        \"type\": \"string\",\n        \"description\": \"A natural language statement explaining the sentiment.\"\n      }\n    },\n    \"required\": [\"sentiment\", \"confidence_score\", \"sentence\"],\n    \"additionalProperties\": false\n  }\n}\n"}, "typeVersion": 1}, {"id": "41352a53-7821-4247-905e-7995e1e6e382", "name": "Initiate a Webhook Notification for Markdown to Textual Data Extraction", "type": "n8n-nodes-base.httpRequest", "position": [1720, -460], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.text }}"}]}}, "typeVersion": 4.2}, {"id": "9717b5df-f148-4c8c-95d4-cb7c54837228", "name": "Initiate a Webhook Notification for AI Sentiment Analyzer", "type": "n8n-nodes-base.httpRequest", "position": [2120, 100], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.output }}"}]}}, "typeVersion": 4.2}, {"id": "88733b5f-cbb0-42a6-898c-7a1ccc94bef7", "name": "Google Gemini Chat Model for Summary", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1260, -780], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "560e3d33-61d8-4db6-b1df-89f4e915f3f1", "name": "Google Gemini Chat Model for Data Extract", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1320, -220], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "1b07608f-7174-46e8-af27-3abf100d9e3a", "name": "Google Gemini Chat Model for Sentiment Analyzer", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "position": [1820, 320], "parameters": {"options": {}, "modelName": "models/gemini-2.0-flash-exp"}, "credentials": {"googlePalmApi": {"id": "YeO7dHZnuGBVQKVZ", "name": "Google Gemini(PaLM) Api account"}}, "typeVersion": 1}, {"id": "b6b6df94-d3fc-45ee-a339-5a368ea000eb", "name": "Initiate a Webhook Notification for Summarization", "type": "n8n-nodes-base.httpRequest", "position": [1660, -820], "parameters": {"url": "https://webhook.site/3c36d7d1-de1b-4171-9fd3-643ea2e4dd76", "options": {}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "summary", "value": "={{ $json.response.text }}"}]}}, "typeVersion": 4.2}, {"id": "f3e60ecd-5d07-4df0-a413-327b24db23ab", "name": "Perform Bright Data Web Request", "type": "n8n-nodes-base.httpRequest", "position": [560, -560], "parameters": {"url": "https://api.brightdata.com/request", "method": "POST", "options": {}, "sendBody": true, "sendHeaders": true, "authentication": "genericCredentialType", "bodyParameters": {"parameters": [{"name": "zone", "value": "={{ $json.zone }}"}, {"name": "url", "value": "={{ $json.url }}?product=unlocker&method=api"}, {"name": "format", "value": "raw"}, {"name": "data_format", "value": "markdown"}]}, "genericAuthType": "httpHeaderAuth", "headerParameters": {"parameters": [{}]}}, "credentials": {"httpHeaderAuth": {"id": "kdbqXuxIR8qIxF7y", "name": "Header Auth account"}}, "typeVersion": 4.2}, {"id": "9030085f-5b05-41d9-94ee-668ee29df815", "name": "Summarize Content", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [1240, -980], "parameters": {"options": {"summarizationMethodAndPrompts": {"values": {"prompt": "Write a concise summary of the following:\n\n\n\"{text}\"\n\n"}}}, "chunkingMode": "advanced"}, "typeVersion": 2}, {"id": "fe93c4a6-de3b-481d-ba6c-5f315f5279c4", "name": "Create a binary data for textual data", "type": "n8n-nodes-base.function", "position": [1720, -220], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "0811c300-1302-49b5-a334-ac8f960a5b8c", "name": "Create a binary data for sentiment analysis", "type": "n8n-nodes-base.function", "position": [2120, 320], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "01d798b7-7c62-4240-9d5e-f2e67ca047ae", "name": "Write the AI Sentiment analysis file to disk", "type": "n8n-nodes-base.readWriteFile", "position": [2520, 320], "parameters": {"options": {}, "fileName": "d:\\Brand-Content-Sentiment-Analysis.json", "operation": "write"}, "typeVersion": 1}, {"id": "f9faf283-ba8d-48e1-860e-2bb660cb9c1e", "name": "Write the textual file to disk", "type": "n8n-nodes-base.readWriteFile", "position": [2100, -220], "parameters": {"options": {}, "fileName": "d:\\Brand-Content-Textual.json", "operation": "write"}, "typeVersion": 1}, {"id": "2c47c271-4456-4fc4-9a54-20784365a4af", "name": "Create a binary data for summary", "type": "n8n-nodes-base.function", "position": [1660, -1060], "parameters": {"functionCode": "items[0].binary = {\n  data: {\n    data: new Buffer(JSON.stringify(items[0].json, null, 2)).toString('base64')\n  }\n};\nreturn items;"}, "typeVersion": 1}, {"id": "c5f33f8d-93eb-47ac-a42f-717b39f4d7c2", "name": "Write the summary file to disk", "type": "n8n-nodes-base.readWriteFile", "position": [1880, -1060], "parameters": {"options": {}, "fileName": "d:\\Brand-Content-Summary.json", "operation": "write"}, "typeVersion": 1}, {"id": "72938f7b-20c1-45d3-9348-878d6e0b8d60", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1200, -1080], "parameters": {"color": 4, "width": 1100, "height": 460, "content": "## Summarization"}, "typeVersion": 1}, {"id": "fcf1d1ad-d516-41bc-bf76-73ebb920ecba", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1720, 40], "parameters": {"color": 6, "width": 1000, "height": 480, "content": "## Sentiment Analysis"}, "typeVersion": 1}, {"id": "9c44d01f-e30b-4597-ad74-09fa54b4ec84", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "position": [1200, -520], "parameters": {"color": 3, "width": 1100, "height": 480, "content": "## Textual Data Extract"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "317a5d48-95c6-4425-a14a-6b2fec9e0802", "connections": {"Summarize Content": {"main": [[{"node": "Initiate a Webhook Notification for Summarization", "type": "main", "index": 0}, {"node": "Create a binary data for summary", "type": "main", "index": 0}]]}, "Set URL and Bright Data Zone": {"main": [[{"node": "Perform Bright Data Web Request", "type": "main", "index": 0}]]}, "Perform Bright Data Web Request": {"main": [[{"node": "Markdown to Textual Data Extractor", "type": "main", "index": 0}, {"node": "Summarize Content", "type": "main", "index": 0}]]}, "Create a binary data for summary": {"main": [[{"node": "Write the summary file to disk", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Set URL and Bright Data Zone", "type": "main", "index": 0}]]}, "Markdown to Textual Data Extractor": {"main": [[{"node": "AI Sentiment Analyzer with the structured response", "type": "main", "index": 0}, {"node": "Initiate a Webhook Notification for Markdown to Textual Data Extraction", "type": "main", "index": 0}, {"node": "Create a binary data for textual data", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for Summary": {"ai_languageModel": [[{"node": "Summarize Content", "type": "ai_languageModel", "index": 0}]]}, "Create a binary data for textual data": {"main": [[{"node": "Write the textual file to disk", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for Data Extract": {"ai_languageModel": [[{"node": "Markdown to Textual Data Extractor", "type": "ai_languageModel", "index": 0}]]}, "Create a binary data for sentiment analysis": {"main": [[{"node": "Write the AI Sentiment analysis file to disk", "type": "main", "index": 0}]]}, "Google Gemini Chat Model for Sentiment Analyzer": {"ai_languageModel": [[{"node": "AI Sentiment Analyzer with the structured response", "type": "ai_languageModel", "index": 0}]]}, "AI Sentiment Analyzer with the structured response": {"main": [[{"node": "Initiate a Webhook Notification for AI Sentiment Analyzer", "type": "main", "index": 0}, {"node": "Create a binary data for sentiment analysis", "type": "main", "index": 0}]]}, "Initiate a Webhook Notification for AI Sentiment Analyzer": {"main": [[]]}, "Initiate a Webhook Notification for Markdown to Textual Data Extraction": {"main": [[]]}}}