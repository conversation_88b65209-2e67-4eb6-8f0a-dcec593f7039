{"id": "wDD4XugmHIvx3KMT", "meta": {"instanceId": "149cdf730f0c143663259ddc6124c9c26e824d8d2d059973b871074cf4bda531"}, "name": "Image Generation API", "tags": [], "nodes": [{"id": "d743f947-ad45-4e59-97d4-79b98eaddedb", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [260, -20], "webhookId": "970dd3c6-de83-46fd-9038-33c470571390", "parameters": {"path": "970dd3c6-de83-46fd-9038-33c470571390", "options": {}, "responseMode": "responseNode"}, "typeVersion": 1.1}, {"id": "832e993e-69e9-475b-8322-776d88da0440", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "position": [1400, -20], "parameters": {"options": {}, "respondWith": "binary"}, "typeVersion": 1}, {"id": "53044a93-375f-48f2-971d-bf765bcdb7a0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [180, -120], "parameters": {"width": 301.7420425026802, "height": 260.80333469825376, "content": "## Webhook Trigger \n**This Node starts listening to requests to the Webhook URL**\n\n"}, "typeVersion": 1}, {"id": "c7b3b04e-903b-4d7c-bbf1-2bc2f1b1a426", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [180, -460], "parameters": {"width": 469.32758643852594, "height": 297.34454352637044, "content": "## Creating your Prompt-URL \n**To use this Workflow you need to append your prompt to your Webhook URL in the following way**\n\n1. Take your Webhook URL\n2. Ideate a Prompt and Replace every Space (\" \") by %20 (Url Encoding)\n3. Append \"?input=\" and right after that your encoded prompt to your url\n4. <PERSON><PERSON> paste this into a webbrowser as soon as you run the Webhook"}, "typeVersion": 1}, {"id": "473ff6e5-441a-4706-86a4-190936cc6ac1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [540, -54.959833265087354], "parameters": {"width": 522.2493371551094, "height": 109.59833265087394, "content": "## Starting the Workflow\n**To start the workflow paste the encoded URL into your webbrowser**\n\n"}, "typeVersion": 1}, {"id": "e8874f52-ef7e-4aea-be5b-81e3276da3d2", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [1120, -20], "parameters": {"prompt": "={{ $json.query.input }}", "options": {}, "resource": "image"}, "typeVersion": 1.1}, {"id": "08c073a6-e01e-4b04-8051-502c918998c4", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [1280, -120], "parameters": {"width": 329.4629595446998, "height": 278.4439182704484, "content": "## Response\n**Watch the image being rendered in your webbrowser**\n\n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "19f7e652-5417-4b02-a1f5-8796bbac25c3", "connections": {"OpenAI": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}}}