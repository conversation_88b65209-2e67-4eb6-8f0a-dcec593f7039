{"id": "xzKlhjcc6QEzA98Z", "meta": {"instanceId": "494d0146a0f47676ad70a44a32086b466621f62da855e3eaf0ee51dee1f76753", "templateId": "2041", "templateCredsSetupCompleted": true}, "name": "Update Roles by Excel", "tags": [], "nodes": [{"id": "580d8a47-32cc-4976-a464-793523ae3d1e", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "position": [80, 140], "parameters": {}, "typeVersion": 1}, {"id": "f37ea772-a953-4b5b-8e54-c76e42938544", "name": "Extract from File", "type": "n8n-nodes-base.extractFromFile", "position": [760, 140], "parameters": {"options": {}, "operation": "xlsx"}, "typeVersion": 1}, {"id": "60ab7913-d421-41cd-af04-ccec2ed6838e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1700, 120], "parameters": {"mode": "combine", "options": {}, "fieldsToMatchString": "email"}, "typeVersion": 3}, {"id": "ad6719b4-95dc-419e-94cb-97039014be62", "name": "Basic Variables", "type": "n8n-nodes-base.set", "position": [320, 140], "parameters": {"options": {}, "assignments": {"assignments": [{"id": "68b32087-5e23-4590-8042-0061234ce479", "name": "zammad_base_url", "type": "string", "value": "https://zammad.sirhexalot.de/"}, {"id": "240f4dc5-a070-4623-96e7-1e0750dbeba5", "name": "excel_source_url", "type": "string", "value": "http://zammad.sirhexalot.de/Users.txt"}]}}, "typeVersion": 3.4}, {"id": "8f18e493-5dbe-4447-a422-450c610e9585", "name": "Zammad Univeral User Object", "type": "n8n-nodes-base.set", "position": [1020, 140], "parameters": {"values": {"string": [{"name": "email", "value": "={{ $json.email }}"}, {"name": "role_ids", "value": "={{ $json.role_ids }}\n"}]}, "options": {}, "keepOnlySet": true}, "typeVersion": 1}, {"id": "5bc0a423-91bc-4b52-af05-2869223bbbff", "name": "Download Excel", "type": "n8n-nodes-base.httpRequest", "position": [540, 140], "parameters": {"url": "={{ $json.excel_source_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "typeVersion": 4.1}, {"id": "b5962a7b-27d3-45f1-adc4-1abff5d1c990", "name": "Find <PERSON><PERSON><PERSON> User by email", "type": "n8n-nodes-base.httpRequest", "position": [1360, -60], "parameters": {"url": "={{ $('Basic Variables').item.json.zammad_base_url }}api/v1/users/search?query=email:{{ $json.email }}", "options": {}, "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "GJ7tG0KxDpEUv3DS", "name": "zammad.sirhexalot.de"}}, "executeOnce": false, "typeVersion": 4.2, "alwaysOutputData": false}, {"id": "0b8f5007-d28d-4406-a7ec-aa69d2b865d5", "name": "Update User Roles", "type": "n8n-nodes-base.httpRequest", "onError": "continueErrorOutput", "position": [2020, 120], "parameters": {"url": "={{ $('Basic Variables').item.json.zammad_base_url }}/api/v1/users/{{ $json.id }}", "method": "PUT", "options": {}, "jsonBody": "={\n  \"role_ids\": [\n  {{ $json.role_ids }}\n  ]\n} ", "sendBody": true, "specifyBody": "json", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "credentials": {"httpHeaderAuth": {"id": "GJ7tG0KxDpEUv3DS", "name": "zammad.sirhexalot.de"}}, "typeVersion": 4.2}, {"id": "7724e271-0beb-4fc3-a9dd-4e55bcf033a1", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [60, -500], "parameters": {"width": 577.5890410958907, "height": 253.58904109589045, "content": "## Authentication for Zammad\n\nCreate in the Node Find Zammad User by email a Header Auth Authentication\n\nUse:\n\nName: Authorization\nValue: Bearer - put here your zammad api token - \n"}, "typeVersion": 1}], "active": false, "pinData": {}, "settings": {"executionOrder": "v1"}, "versionId": "2e34f31f-cb00-43e1-8709-6405ea8521ac", "connections": {"Merge": {"main": [[{"node": "Update User Roles", "type": "main", "index": 0}]]}, "Download Excel": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Basic Variables": {"main": [[{"node": "Download Excel", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Zammad Univeral User Object", "type": "main", "index": 0}]]}, "Find Zammad User by email": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Zammad Univeral User Object": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "Find <PERSON><PERSON><PERSON> User by email", "type": "main", "index": 0}]]}, "When clicking \"Execute Workflow\"": {"main": [[{"node": "Basic Variables", "type": "main", "index": 0}]]}}}